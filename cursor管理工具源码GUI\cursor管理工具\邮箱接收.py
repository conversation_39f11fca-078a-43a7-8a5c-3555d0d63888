import poplib
import email
import re
from bs4 import BeautifulSoup
import time
import sys
import os
import queue

# 使用绝对路径确保文件位置统一
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# --- 全局配置 ---
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
# 监控时每次刷新的间隔时间（秒）
REFRESH_INTERVAL_SECONDS = 1

# --- 硬编码的凭据 ---
# EMAIL_ACCOUNT = "<EMAIL>" # 已废弃
# EMAIL_PASSWORD = "Sun20030927" # 已废弃

def decode_payload(payload, charset):
    """安全地用给定字符集解码负载。"""
    try:
        return payload.decode(charset)
    except (UnicodeDecodeError, LookupError):
        # 回退到gbk以处理常见中文邮件，忽略错误
        return payload.decode('gbk', errors='ignore')

def get_clean_body_from_msg(msg):
    """解析一个email.message对象并返回纯净的文本正文。"""
    body_content, html_content = "", ""
    if msg.is_multipart():
        for part in msg.walk():
            # 跳过附件
            if part.get('Content-Disposition', '').startswith('attachment'):
                continue
            payload = part.get_payload(decode=True)
            if not payload:
                continue
            charset = part.get_content_charset() or 'utf-8'
            content_type = part.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    else:  # 单部分邮件
        if not msg.get('Content-Disposition', '').startswith('attachment'):
            payload = msg.get_payload(decode=True)
            charset = msg.get_content_charset() or 'utf-8'
            content_type = msg.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    if not body_content.strip() and html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        return soup.get_text(separator='\n', strip=True)
    return body_content

def find_code_in_text(body_text):
    """使用正则表达式在字符串中查找6位验证码。"""
    # 匹配格式: 123456, 123 456, 1 2 3 4 5 6
    patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
    for pattern in patterns:
        match = re.search(pattern, body_text)
        if match:
            # 返回去除了空格的验证码
            return match.group(0).replace(" ", "")
    return None

def establish_baseline(server, output_queue):
    """获取当前所有邮件的UIDL，建立基线。"""
    try:
        resp, uid_lines, octets = server.uidl()
        # 将UIDL列表转换为一个UID集合，用于快速查找
        seen_uids = {line.split()[1] for line in uid_lines}
        output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
        return seen_uids
    except Exception as e:
        output_queue.put(f"建立基线时出错: {e}")
        return None

def main(email_account, email_password, output_queue):
    """主执行逻辑：建立基线，然后持续监控新邮件。"""
    output_queue.put(f"正在监控邮箱: {email_account}")
    
    # --- 1. 首次连接并建立基线 ---
    try:
        server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
        server.user(email_account)
        server.pass_(email_password)
        output_queue.put("验证成功。")
        seen_uids = establish_baseline(server, output_queue)
        server.quit()
        if seen_uids is None:
            return
    except poplib.error_proto as e:
        output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
        return
    except Exception as e:
        output_queue.put(f"连接或建立基线时发生未知错误: {e}")
        return

    # --- 2. 进入监控循环 ---
    loop_counter = 0
    while True:
        try:
            # 添加一个停止标志的检查
            try:
                if output_queue.get_nowait() == 'STOP':
                    break
            except queue.Empty:
                pass

            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(email_account)
            server.pass_(email_password)
            
            resp, uid_lines, octets = server.uidl()
            
            # 创建当前邮件UID到消息号的映射
            current_uid_map = {
                parts[1]: parts[0]
                for line in uid_lines
                if len(parts := line.split()) == 2
            }

            # 找出新的UID
            new_uids = set(current_uid_map.keys()) - seen_uids
            
            if new_uids:
                loop_counter = 0 # 重置计数器
                output_queue.put(f"\n发现 {len(new_uids)} 封新邮件，正在检查...")
                # 将新邮件按消息号排序，确保先检查最新的
                new_messages = sorted(
                    [(int(current_uid_map[uid]), uid) for uid in new_uids],
                    key=lambda x: x[0],
                    reverse=True
                )

                for msg_num, uid in new_messages:
                    resp, lines, octets = server.retr(msg_num)
                    msg_content = b'\r\n'.join(lines)
                    msg = email.message_from_bytes(msg_content)
                    
                    body = get_clean_body_from_msg(msg)
                    code = find_code_in_text(body)
                    
                    if code:
                        # 打印验证码给用户看
                        output_queue.put(f"成功提取到新邮件中的验证码: {code}")
                        # 打印一个特殊格式的字符串，用于进程间通信
                        output_queue.put(f"VERIFICATION_CODE:{code}")
                        
                        server.quit()
                        return # 任务完成，退出程序
                
                # 如果检查完所有新邮件都没找到验证码，则更新基线
                output_queue.put("新邮件中未发现验证码，将继续监控...")
                seen_uids.update(new_uids)
            else:
                loop_counter += 1
                # 每 15 秒打印一次状态，避免刷屏
                if loop_counter % 15 == 1:
                    output_queue.put(f"没有新邮件，继续监控... ({time.strftime('%H:%M:%S')})")


            server.quit()
            time.sleep(REFRESH_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            output_queue.put("\n程序已手动停止。")
            break
        except Exception as e:
            output_queue.put(f"\n监控循环中发生错误: {e}。等待10秒后重试...")
            time.sleep(10)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("错误: 脚本需要两个参数: 邮箱账号和邮箱密码。")
        print(f"用法: python {sys.argv[0]} <email_address> <password>")
        sys.exit(1)
    
    # This part is for standalone testing and is not used by the main app.
    # A simple queue for testing purposes.
    class SimpleQueue:
        def put(self, item):
            print(item)
        def get_nowait(self):
            raise queue.Empty

    account = sys.argv[1]
    password = sys.argv[2]
    main(account, password, SimpleQueue()) 