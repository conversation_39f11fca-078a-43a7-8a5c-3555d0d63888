<Application x:Class="CursorManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemeResources />
                <ui:XamlControlsResources />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局样式 -->
            <Style x:Key="CardStyle" TargetType="Border">
                <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumLowBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="4"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
            </Style>
            
            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="0,0,0,12"/>
                <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
            </Style>
            
            <Style x:Key="StatusTextStyle" TargetType="TextBox">
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
                <Setter Property="FontFamily" Value="Consolas"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumLowBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
