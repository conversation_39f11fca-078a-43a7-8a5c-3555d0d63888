using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Windows;
using CursorManager.Services;
using CursorManager.ViewModels;
using CursorManager.Views;

namespace CursorManager;

public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 创建主机
        _host = Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册配置
                services.Configure<AppSettings>(context.Configuration.GetSection("AppSettings"));
                services.Configure<EmailSettings>(context.Configuration.GetSection("Email"));
                services.Configure<BrowserSettings>(context.Configuration.GetSection("Browser"));
                services.Configure<TimingSettings>(context.Configuration.GetSection("Timing"));

                // 注册服务
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                services.AddSingleton<IEmailService, EmailService>();
                services.AddSingleton<IBrowserService, BrowserService>();
                services.AddSingleton<IApplicationService, ApplicationService>();
                services.AddSingleton<IUtilityService, UtilityService>();

                // 注册ViewModels
                services.AddTransient<MainViewModel>();

                // 注册Views
                services.AddTransient<MainWindow>();
            })
            .ConfigureLogging(logging =>
            {
                logging.AddConsole();
                logging.AddDebug();
            })
            .Build();

        await _host.StartAsync();

        // 显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }
}

// 配置模型
public class AppSettings
{
    public string AppName { get; set; } = "续杯工具";
    public string Version { get; set; } = "1.0.0";
    public string ConfigPath { get; set; } = "";
}

public class EmailSettings
{
    public string Prefix { get; set; } = "";
    public string Password { get; set; } = "";
    public string Server { get; set; } = "pop.2925.com";
    public int Port { get; set; } = 110;
    public string Domain { get; set; } = "2925.com";
}

public class BrowserSettings
{
    public string ChromePath { get; set; } = "";
    public List<string> DefaultPaths { get; set; } = new();
}

public class TimingSettings
{
    public double MinRandomTime { get; set; } = 0.1;
    public double MaxRandomTime { get; set; } = 0.8;
    public string PageLoadWait { get; set; } = "0.1-0.8";
    public string InputWait { get; set; } = "0.3-0.8";
    public string SubmitWait { get; set; } = "0.5-1.5";
    public string VerificationCodeInput { get; set; } = "0.1-0.3";
    public string VerificationSuccessWait { get; set; } = "2-3";
    public string VerificationRetryWait { get; set; } = "2-3";
    public string EmailCheckInitialWait { get; set; } = "4-6";
    public string EmailRefreshWait { get; set; } = "2-4";
    public string SettingsPageLoadWait { get; set; } = "1-2";
    public string FailedRetryTime { get; set; } = "0.5-1";
    public string RetryInterval { get; set; } = "8-12";
    public int MaxTimeout { get; set; } = 160;
}
