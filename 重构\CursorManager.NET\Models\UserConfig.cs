using System.ComponentModel.DataAnnotations;

namespace CursorManager.Models;

public class UserConfig
{
    [Required]
    public string EmailPrefix { get; set; } = "";
    
    public string EmailPassword { get; set; } = "";
    
    public string ChromePath { get; set; } = "";
    
    public DateTime LastUpdated { get; set; } = DateTime.Now;
    
    public Dictionary<string, object> ExtendedSettings { get; set; } = new();

    // 验证配置是否有效
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(EmailPrefix) && 
               !string.IsNullOrWhiteSpace(EmailPassword);
    }

    // 获取完整的监控邮箱地址
    public string GetMonitoringEmail()
    {
        return string.IsNullOrWhiteSpace(EmailPrefix) ? "" : $"{EmailPrefix}@2925.com";
    }

    // 生成随机登录邮箱
    public string GenerateRandomLoginEmail()
    {
        if (string.IsNullOrWhiteSpace(EmailPrefix))
            return "";

        var random = new Random();
        var suffix = new string(Enumerable.Repeat("abcdefghijklmnopqrstuvwxyz0123456789", 8)
                                         .Select(s => s[random.Next(s.Length)]).ToArray());
        
        return $"{EmailPrefix}{suffix}@2925.com";
    }

    // 克隆配置
    public UserConfig Clone()
    {
        return new UserConfig
        {
            EmailPrefix = EmailPrefix,
            EmailPassword = EmailPassword,
            ChromePath = ChromePath,
            LastUpdated = LastUpdated,
            ExtendedSettings = new Dictionary<string, object>(ExtendedSettings)
        };
    }
}

// 邮件消息模型
public class EmailMessage
{
    public int Id { get; set; }
    public string Subject { get; set; } = "";
    public string From { get; set; } = "";
    public string Body { get; set; } = "";
    public DateTime ReceivedDate { get; set; }
    public bool IsRead { get; set; }
    public string? VerificationCode { get; set; }
}

// 状态消息模型
public class StatusMessage
{
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string Message { get; set; } = "";
    public StatusLevel Level { get; set; } = StatusLevel.Info;
    public string? Details { get; set; }

    public override string ToString()
    {
        var prefix = Level switch
        {
            StatusLevel.Success => "✅",
            StatusLevel.Warning => "⚠️",
            StatusLevel.Error => "❌",
            StatusLevel.Info => "ℹ️",
            _ => ""
        };

        return $"[{Timestamp:HH:mm:ss}] {prefix} {Message}";
    }
}

public enum StatusLevel
{
    Info,
    Success,
    Warning,
    Error
}

// 操作结果模型
public class OperationResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = "";
    public Exception? Exception { get; set; }
    public object? Data { get; set; }

    public static OperationResult Success(string message = "", object? data = null)
    {
        return new OperationResult
        {
            IsSuccess = true,
            Message = message,
            Data = data
        };
    }

    public static OperationResult Failure(string message, Exception? exception = null)
    {
        return new OperationResult
        {
            IsSuccess = false,
            Message = message,
            Exception = exception
        };
    }
}

// 验证码提取结果
public class VerificationCodeResult
{
    public bool Found { get; set; }
    public string Code { get; set; } = "";
    public string Source { get; set; } = "";
    public DateTime ExtractedAt { get; set; } = DateTime.Now;
}
