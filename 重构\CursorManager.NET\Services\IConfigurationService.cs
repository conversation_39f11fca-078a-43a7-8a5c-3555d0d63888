using CursorManager.Models;

namespace CursorManager.Services;

public interface IConfigurationService
{
    Task<UserConfig> LoadConfigAsync();
    Task SaveConfigAsync(UserConfig config);
    Task<string> GetUserConfigPathAsync();
    Task<string> GetDefaultBrowserPathAsync();
    Task<bool> ValidateBrowserPathAsync(string path);
    Task ResetToDefaultsAsync();
}

public class ConfigurationService : IConfigurationService
{
    private readonly string _configDirectory;
    private readonly string _configFilePath;

    public ConfigurationService()
    {
        var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        _configDirectory = Path.Combine(documentsPath, "续杯工具");
        _configFilePath = Path.Combine(_configDirectory, "config.json");
    }

    public async Task<UserConfig> LoadConfigAsync()
    {
        try
        {
            // 确保配置目录存在
            Directory.CreateDirectory(_configDirectory);

            if (!File.Exists(_configFilePath))
            {
                // 创建默认配置
                var defaultConfig = CreateDefaultConfig();
                await SaveConfigAsync(defaultConfig);
                return defaultConfig;
            }

            var json = await File.ReadAllTextAsync(_configFilePath);
            var config = System.Text.Json.JsonSerializer.Deserialize<UserConfig>(json);
            return config ?? CreateDefaultConfig();
        }
        catch (Exception ex)
        {
            // 记录错误并返回默认配置
            System.Diagnostics.Debug.WriteLine($"加载配置失败: {ex.Message}");
            return CreateDefaultConfig();
        }
    }

    public async Task SaveConfigAsync(UserConfig config)
    {
        try
        {
            Directory.CreateDirectory(_configDirectory);
            
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            
            var json = System.Text.Json.JsonSerializer.Serialize(config, options);
            await File.WriteAllTextAsync(_configFilePath, json);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"保存配置失败: {ex.Message}", ex);
        }
    }

    public Task<string> GetUserConfigPathAsync()
    {
        return Task.FromResult(_configDirectory);
    }

    public Task<string> GetDefaultBrowserPathAsync()
    {
        var possiblePaths = new[]
        {
            @"C:\Program Files\Google\Chrome\Application\chrome.exe",
            @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                        @"Google\Chrome\Application\chrome.exe")
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                return Task.FromResult(path);
            }
        }

        return Task.FromResult(string.Empty);
    }

    public Task<bool> ValidateBrowserPathAsync(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
            return Task.FromResult(false);

        try
        {
            return Task.FromResult(File.Exists(path) && 
                                 path.EndsWith(".exe", StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return Task.FromResult(false);
        }
    }

    public async Task ResetToDefaultsAsync()
    {
        var defaultConfig = CreateDefaultConfig();
        await SaveConfigAsync(defaultConfig);
    }

    private UserConfig CreateDefaultConfig()
    {
        return new UserConfig
        {
            EmailPrefix = "",
            EmailPassword = "",
            ChromePath = GetDefaultBrowserPathAsync().Result,
            LastUpdated = DateTime.Now
        };
    }
}
