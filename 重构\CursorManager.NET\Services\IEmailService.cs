using CursorManager.Models;
using MailKit.Net.Pop3;
using MailKit;
using MimeKit;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace CursorManager.Services;

public interface IEmailService
{
    event EventHandler<VerificationCodeResult>? VerificationCodeReceived;
    event EventHandler<StatusMessage>? StatusUpdated;
    
    Task<bool> TestConnectionAsync(string email, string password);
    Task StartMonitoringAsync(string email, string password, CancellationToken cancellationToken = default);
    Task StopMonitoringAsync();
    bool IsMonitoring { get; }
}

public class EmailService : IEmailService, IDisposable
{
    private readonly ILogger<EmailService> _logger;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _monitoringTask;
    private readonly HashSet<string> _seenMessageIds = new();
    private bool _disposed;

    public event EventHandler<VerificationCodeResult>? VerificationCodeReceived;
    public event EventHandler<StatusMessage>? StatusUpdated;

    public bool IsMonitoring => _monitoringTask?.IsCompleted == false;

    public EmailService(ILogger<EmailService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> TestConnectionAsync(string email, string password)
    {
        try
        {
            using var client = new Pop3Client();
            await client.ConnectAsync("pop.2925.com", 110, false);
            await client.AuthenticateAsync(email, password);
            await client.DisconnectAsync(true);
            
            OnStatusUpdated(new StatusMessage 
            { 
                Message = "邮箱连接测试成功", 
                Level = StatusLevel.Success 
            });
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "邮箱连接测试失败");
            OnStatusUpdated(new StatusMessage 
            { 
                Message = $"邮箱连接测试失败: {ex.Message}", 
                Level = StatusLevel.Error 
            });
            return false;
        }
    }

    public async Task StartMonitoringAsync(string email, string password, CancellationToken cancellationToken = default)
    {
        if (IsMonitoring)
        {
            await StopMonitoringAsync();
        }

        _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        
        OnStatusUpdated(new StatusMessage 
        { 
            Message = $"开始监控邮箱: {email}", 
            Level = StatusLevel.Info 
        });

        // 建立基线
        await EstablishBaselineAsync(email, password);

        // 开始监控循环
        _monitoringTask = MonitorEmailsAsync(email, password, _cancellationTokenSource.Token);
    }

    public async Task StopMonitoringAsync()
    {
        if (_cancellationTokenSource != null)
        {
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _cancellationTokenSource = null;
        }

        if (_monitoringTask != null)
        {
            try
            {
                await _monitoringTask;
            }
            catch (OperationCanceledException)
            {
                // 预期的取消操作
            }
            _monitoringTask = null;
        }

        OnStatusUpdated(new StatusMessage 
        { 
            Message = "邮箱监控已停止", 
            Level = StatusLevel.Info 
        });
    }

    private async Task EstablishBaselineAsync(string email, string password)
    {
        try
        {
            using var client = new Pop3Client();
            await client.ConnectAsync("pop.2925.com", 110, false);
            await client.AuthenticateAsync(email, password);

            var messageCount = client.Count;
            
            // 记录现有邮件的UID
            for (int i = 0; i < messageCount; i++)
            {
                var uid = client.GetMessageUid(i);
                _seenMessageIds.Add(uid);
            }

            await client.DisconnectAsync(true);
            
            OnStatusUpdated(new StatusMessage 
            { 
                Message = $"基线已建立，当前有 {messageCount} 封邮件", 
                Level = StatusLevel.Success 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立邮箱基线失败");
            OnStatusUpdated(new StatusMessage 
            { 
                Message = $"建立邮箱基线失败: {ex.Message}", 
                Level = StatusLevel.Error 
            });
            throw;
        }
    }

    private async Task MonitorEmailsAsync(string email, string password, CancellationToken cancellationToken)
    {
        var loopCounter = 0;
        
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                await CheckForNewEmailsAsync(email, password, cancellationToken);
                
                loopCounter++;
                if (loopCounter % 30 == 0) // 每30次循环显示一次状态
                {
                    OnStatusUpdated(new StatusMessage 
                    { 
                        Message = "正在监控中...", 
                        Level = StatusLevel.Info 
                    });
                }

                await Task.Delay(1000, cancellationToken); // 每秒检查一次
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "邮箱监控过程中发生错误");
                OnStatusUpdated(new StatusMessage 
                { 
                    Message = $"监控过程中发生错误: {ex.Message}", 
                    Level = StatusLevel.Warning 
                });
                
                // 等待一段时间后重试
                await Task.Delay(5000, cancellationToken);
            }
        }
    }

    private async Task CheckForNewEmailsAsync(string email, string password, CancellationToken cancellationToken)
    {
        using var client = new Pop3Client();
        await client.ConnectAsync("pop.2925.com", 110, false, cancellationToken);
        await client.AuthenticateAsync(email, password, cancellationToken);

        var messageCount = client.Count;
        var newMessages = new List<(int index, string uid)>();

        // 查找新邮件
        for (int i = 0; i < messageCount; i++)
        {
            var uid = client.GetMessageUid(i);
            if (!_seenMessageIds.Contains(uid))
            {
                newMessages.Add((i, uid));
                _seenMessageIds.Add(uid);
            }
        }

        if (newMessages.Count > 0)
        {
            OnStatusUpdated(new StatusMessage 
            { 
                Message = $"发现 {newMessages.Count} 封新邮件，正在检查...", 
                Level = StatusLevel.Info 
            });

            // 检查新邮件中的验证码（按最新的优先）
            foreach (var (index, uid) in newMessages.OrderByDescending(x => x.index))
            {
                try
                {
                    var message = await client.GetMessageAsync(index, cancellationToken);
                    var verificationCode = ExtractVerificationCode(message);
                    
                    if (!string.IsNullOrEmpty(verificationCode))
                    {
                        OnStatusUpdated(new StatusMessage 
                        { 
                            Message = $"成功提取到验证码: {verificationCode}", 
                            Level = StatusLevel.Success 
                        });

                        OnVerificationCodeReceived(new VerificationCodeResult
                        {
                            Found = true,
                            Code = verificationCode,
                            Source = $"邮件 {uid}",
                            ExtractedAt = DateTime.Now
                        });

                        break; // 找到验证码后停止检查
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "处理邮件 {Uid} 时发生错误", uid);
                }
            }
        }

        await client.DisconnectAsync(true, cancellationToken);
    }

    private string ExtractVerificationCode(MimeMessage message)
    {
        var content = message.TextBody ?? message.HtmlBody ?? "";
        
        // 常见的验证码模式
        var patterns = new[]
        {
            @"(?i)verification\s*code[:\s]*([A-Z0-9]{6})",
            @"(?i)verify\s*code[:\s]*([A-Z0-9]{6})",
            @"(?i)code[:\s]*([A-Z0-9]{6})",
            @"\b([A-Z0-9]{6})\b"
        };

        foreach (var pattern in patterns)
        {
            var match = Regex.Match(content, pattern);
            if (match.Success && match.Groups.Count > 1)
            {
                var code = match.Groups[1].Value.Trim();
                if (code.Length == 6)
                {
                    return code;
                }
            }
        }

        return "";
    }

    private void OnStatusUpdated(StatusMessage status)
    {
        StatusUpdated?.Invoke(this, status);
    }

    private void OnVerificationCodeReceived(VerificationCodeResult result)
    {
        VerificationCodeReceived?.Invoke(this, result);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            StopMonitoringAsync().Wait(5000);
            _disposed = true;
        }
    }
}
