# Cursor管理工具 - Go版本

这是原Python版本Cursor管理工具的Go语言重构版本，只保留GUI功能。

## 功能特性

- 🖥️ 现代化GUI界面（使用Fyne）
- 📧 自动邮箱管理和验证码接收
- 🌐 浏览器自动化操作
- ⚙️ 配置管理
- 🔄 Cursor应用重置和管理
- 📋 剪贴板操作

## 项目结构

```
cursor-manager-go/
├── cmd/
│   └── main.go              # 主程序入口
├── internal/
│   ├── config/              # 配置管理
│   ├── email/               # 邮箱接收
│   ├── browser/             # 网页操作
│   ├── app/                 # 应用管理
│   ├── utils/               # 工具模块
│   └── gui/                 # GUI界面
├── pkg/                     # 公共包
├── go.mod
└── README.md
```

## 依赖库

- **fyne.io/fyne/v2** - GUI框架
- **github.com/chromedp/chromedp** - 浏览器自动化
- **github.com/spf13/viper** - 配置管理
- **github.com/emersion/go-pop3** - POP3邮箱客户端
- **golang.org/x/sys** - 系统调用

## 构建和运行

```bash
# 下载依赖
go mod tidy

# 构建
go build -o cursor-manager.exe ./cmd

# 运行
./cursor-manager.exe
```

## 使用说明

1. 启动程序后，首先配置邮箱前缀和密码
2. 设置Chrome浏览器路径（可选）
3. 使用一键登录功能自动处理Cursor登录流程
4. 使用邮箱客户端监控验证码
5. 使用环境重置功能清理Cursor相关数据

## 注意事项

- 需要Windows系统支持
- 需要安装Chrome浏览器
- 确保网络连接正常
- 首次使用需要配置邮箱信息
