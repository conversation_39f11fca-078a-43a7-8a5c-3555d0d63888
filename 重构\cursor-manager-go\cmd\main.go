package main

import (
	"log"
	"os"

	"cursor-manager-go/internal/gui"
)

func main() {
	// 设置日志输出
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	
	// 创建日志文件
	logFile, err := os.OpenFile("cursor-manager.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("无法创建日志文件: %v", err)
	} else {
		defer logFile.Close()
		log.SetOutput(logFile)
	}

	// 启动GUI应用
	app := gui.NewApp()
	app.Run()
}
