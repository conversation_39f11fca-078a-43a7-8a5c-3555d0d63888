package app

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"cursor-manager-go/internal/utils"
)

// Manager 应用管理器
type Manager struct {
	statusCallback func(string)
}

// NewManager 创建新的应用管理器
func NewManager(statusCallback func(string)) *Manager {
	return &Manager{
		statusCallback: statusCallback,
	}
}

// MachineIDResetter 机器ID重置器
type MachineIDResetter struct {
	statusCallback func(string)
}

// NewMachineIDResetter 创建机器ID重置器
func NewMachineIDResetter(statusCallback func(string)) *MachineIDResetter {
	return &MachineIDResetter{
		statusCallback: statusCallback,
	}
}

// ResetMachineID 重置机器ID
func (r *MachineIDResetter) ResetMachineID() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("此功能仅支持Windows系统")
	}
	
	r.statusCallback("开始重置机器ID...")
	
	// 获取Cursor相关路径
	paths := r.getCursorPaths()
	
	// 停止Cursor进程
	r.statusCallback("正在停止Cursor进程...")
	err := r.terminateCursorProcesses()
	if err != nil {
		r.statusCallback(fmt.Sprintf("警告: 停止进程时出错: %v", err))
	}
	
	// 删除Cursor相关文件和目录
	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			r.statusCallback(fmt.Sprintf("正在删除: %s", path))
			err = os.RemoveAll(path)
			if err != nil {
				r.statusCallback(fmt.Sprintf("警告: 删除失败: %v", err))
			} else {
				r.statusCallback(fmt.Sprintf("成功删除: %s", path))
			}
		}
	}
	
	// 清理注册表
	err = r.cleanRegistry()
	if err != nil {
		r.statusCallback(fmt.Sprintf("警告: 清理注册表失败: %v", err))
	}
	
	r.statusCallback("机器ID重置完成！")
	return nil
}

// getCursorPaths 获取Cursor相关路径
func (r *MachineIDResetter) getCursorPaths() []string {
	homeDir, _ := os.UserHomeDir()
	appData := os.Getenv("APPDATA")
	localAppData := os.Getenv("LOCALAPPDATA")
	
	paths := []string{
		filepath.Join(appData, "Cursor"),
		filepath.Join(localAppData, "Cursor"),
		filepath.Join(homeDir, ".cursor"),
		filepath.Join(homeDir, ".cursor-server"),
	}
	
	// 添加可能的安装路径
	programFiles := os.Getenv("PROGRAMFILES")
	programFilesX86 := os.Getenv("PROGRAMFILES(X86)")
	
	if programFiles != "" {
		paths = append(paths, filepath.Join(programFiles, "Cursor"))
	}
	if programFilesX86 != "" {
		paths = append(paths, filepath.Join(programFilesX86, "Cursor"))
	}
	
	return paths
}

// terminateCursorProcesses 终止Cursor进程
func (r *MachineIDResetter) terminateCursorProcesses() error {
	processNames := []string{
		"Cursor.exe",
		"cursor.exe",
		"cursor-server.exe",
		"cursor-updater.exe",
	}
	
	for _, processName := range processNames {
		if utils.IsProcessRunning(processName) {
			r.statusCallback(fmt.Sprintf("正在终止进程: %s", processName))
			err := utils.KillProcess(processName)
			if err != nil {
				r.statusCallback(fmt.Sprintf("警告: 终止进程失败: %v", err))
			} else {
				r.statusCallback(fmt.Sprintf("成功终止进程: %s", processName))
			}
		}
	}
	
	// 等待进程完全终止
	time.Sleep(2 * time.Second)
	return nil
}

// cleanRegistry 清理注册表
func (r *MachineIDResetter) cleanRegistry() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("此功能仅支持Windows系统")
	}
	
	r.statusCallback("正在清理注册表...")
	
	// 清理用户注册表项
	userKeys := []string{
		`HKEY_CURRENT_USER\Software\Cursor`,
		`HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall\Cursor`,
	}
	
	for _, key := range userKeys {
		cmd := exec.Command("reg", "delete", key, "/f")
		err := cmd.Run()
		if err == nil {
			r.statusCallback(fmt.Sprintf("成功删除注册表项: %s", key))
		}
	}
	
	return nil
}

// LaunchCursor 启动Cursor
func (m *Manager) LaunchCursor() error {
	m.statusCallback("正在启动Cursor...")
	
	// 常见的Cursor安装路径
	possiblePaths := []string{
		`C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe`,
		`C:\Program Files\Cursor\Cursor.exe`,
		`C:\Program Files (x86)\Cursor\Cursor.exe`,
	}
	
	var cursorPath string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			cursorPath = path
			break
		}
	}
	
	if cursorPath == "" {
		return fmt.Errorf("未找到Cursor安装路径")
	}
	
	cmd := exec.Command(cursorPath)
	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动Cursor失败: %v", err)
	}
	
	m.statusCallback("Cursor已启动")
	return nil
}

// TerminateCursorProcesses 终止Cursor进程
func (m *Manager) TerminateCursorProcesses() error {
	resetter := NewMachineIDResetter(m.statusCallback)
	return resetter.terminateCursorProcesses()
}

// RunFullResetFlow 运行完整重置流程
func (m *Manager) RunFullResetFlow() error {
	m.statusCallback(fmt.Sprintf("%s 开始完整环境重置流程...", utils.Emoji["INFO"]))
	
	// 步骤1: 重置机器ID
	resetter := NewMachineIDResetter(m.statusCallback)
	err := resetter.ResetMachineID()
	if err != nil {
		m.statusCallback(fmt.Sprintf("%s 重置机器ID失败: %v", utils.Emoji["ERROR"], err))
		return err
	}
	
	// 步骤2: 等待一段时间
	m.statusCallback("等待系统稳定...")
	time.Sleep(3 * time.Second)
	
	// 步骤3: 重新启动Cursor
	err = m.LaunchCursor()
	if err != nil {
		m.statusCallback(fmt.Sprintf("%s 启动Cursor失败: %v", utils.Emoji["WARNING"], err))
		// 不返回错误，因为用户可以手动启动
	}
	
	m.statusCallback(fmt.Sprintf("%s 完整环境重置流程完成！", utils.Emoji["SUCCESS"]))
	return nil
}

// EmailClientProcess 邮箱客户端进程管理
type EmailClientProcess struct {
	statusCallback func(string)
}

// NewEmailClientProcess 创建邮箱客户端进程管理器
func NewEmailClientProcess(statusCallback func(string)) *EmailClientProcess {
	return &EmailClientProcess{
		statusCallback: statusCallback,
	}
}

// Launch 启动邮箱客户端进程
func (ecp *EmailClientProcess) Launch(emailToWatch, password string) error {
	if emailToWatch == "" || password == "" {
		return fmt.Errorf("邮箱地址或密码为空")
	}
	
	ecp.statusCallback(fmt.Sprintf("正在启动邮箱监控: %s", emailToWatch))
	
	// 这里应该启动邮箱监控goroutine
	// 由于Go的并发特性，我们可以直接在这里启动监控
	go func() {
		// TODO: 集成邮箱监控模块
		ecp.statusCallback("邮箱监控已启动")
	}()
	
	return nil
}

// GetCursorInstallPath 获取Cursor安装路径
func GetCursorInstallPath() string {
	possiblePaths := []string{
		`C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe`,
		`C:\Program Files\Cursor\Cursor.exe`,
		`C:\Program Files (x86)\Cursor\Cursor.exe`,
	}
	
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}
	
	return ""
}

// IsCursorRunning 检查Cursor是否运行
func IsCursorRunning() bool {
	return utils.IsProcessRunning("Cursor.exe") || utils.IsProcessRunning("cursor.exe")
}

// GetCursorVersion 获取Cursor版本信息
func GetCursorVersion() string {
	cursorPath := GetCursorInstallPath()
	if cursorPath == "" {
		return "未安装"
	}
	
	// 尝试获取版本信息
	cmd := exec.Command(cursorPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return "未知版本"
	}
	
	version := strings.TrimSpace(string(output))
	return version
}
