package browser

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/chromedp/chromedp"
	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
)

const (
	MaxWaitSeconds = 90
)

// AutomationClient 浏览器自动化客户端
type AutomationClient struct {
	ctx    context.Context
	cancel context.CancelFunc
	config *config.Config
}

// NewAutomationClient 创建新的自动化客户端
func NewAutomationClient() *AutomationClient {
	cfg := config.GetConfig()
	return &AutomationClient{
		config: cfg,
	}
}

// GenerateRandomEmail 生成随机邮箱
func (ac *AutomationClient) GenerateRandomEmail() string {
	cfg := config.GetConfig()
	prefix := cfg.Email.Prefix
	
	if prefix == "" {
		return ""
	}
	
	// 生成随机后缀
	suffix := utils.GenerateRandomString(8)
	return fmt.Sprintf("%<EMAIL>", prefix, suffix)
}

// StartBrowser 启动浏览器
func (ac *AutomationClient) StartBrowser() error {
	// 设置Chrome选项
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("incognito", true),
		chromedp.Flag("disable-blink-features", "AutomationControlled"),
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("disable-features", "VizDisplayCompositor"),
	)
	
	// 如果配置了Chrome路径，使用配置的路径
	if ac.config.Browser.ChromePath != "" {
		opts = append(opts, chromedp.ExecPath(ac.config.Browser.ChromePath))
	}
	
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	ac.cancel = cancel
	
	ctx, _ := chromedp.NewContext(allocCtx)
	ac.ctx = ctx
	
	return nil
}

// CloseBrowser 关闭浏览器
func (ac *AutomationClient) CloseBrowser() {
	if ac.cancel != nil {
		ac.cancel()
	}
}

// NavigateToURL 导航到指定URL
func (ac *AutomationClient) NavigateToURL(url string) error {
	if !strings.HasPrefix(url, "http") {
		url = "https://" + url
	}
	
	return chromedp.Run(ac.ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible("body", chromedp.ByQuery),
	)
}

// AutomateLogin 自动化登录流程
func (ac *AutomationClient) AutomateLogin(url, email string, statusCallback func(string), codeContainer *string) error {
	err := ac.StartBrowser()
	if err != nil {
		return fmt.Errorf("启动浏览器失败: %v", err)
	}
	defer ac.CloseBrowser()
	
	statusCallback(fmt.Sprintf("正在加载URL: %s", url))
	
	// 导航到URL
	err = ac.NavigateToURL(url)
	if err != nil {
		return fmt.Errorf("导航到URL失败: %v", err)
	}
	
	// 步骤1: 填写邮箱并点击Continue
	statusCallback("步骤 1: 正在填写邮箱并点击 'Continue'...")
	
	err = chromedp.Run(ac.ctx,
		chromedp.WaitVisible(`input[name="email"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="email"]`, email, chromedp.ByQuery),
		chromedp.Sleep(utils.GetRandomWaitTime("0.3-0.8")),
		chromedp.Click(`input[type="submit"]`, chromedp.ByQuery),
	)
	if err != nil {
		return fmt.Errorf("填写邮箱失败: %v", err)
	}
	
	statusCallback("已点击 'Continue'，等待跳转...")
	
	// 步骤2: 点击Email sign-in code
	statusCallback("步骤 2: 等待密码页面加载，准备点击验证码登录...")
	
	err = chromedp.Run(ac.ctx,
		chromedp.WaitVisible(`text:Email sign-in code`, chromedp.ByQuery),
		chromedp.Sleep(utils.GetRandomWaitTime("1-2")),
		chromedp.Click(`text:Email sign-in code`, chromedp.ByQuery),
	)
	if err != nil {
		return fmt.Errorf("点击验证码登录失败: %v", err)
	}
	
	statusCallback("已点击 'Email sign-in code' 按钮。")
	
	// 步骤3: 等待验证码输入页面
	statusCallback("步骤 3: 等待验证码输入页面加载...")
	
	err = chromedp.Run(ac.ctx,
		chromedp.WaitVisible(`input[type="text"]`, chromedp.ByQuery),
	)
	if err != nil {
		return fmt.Errorf("等待验证码输入页面失败: %v", err)
	}
	
	statusCallback("验证码输入页面已加载，等待验证码...")
	
	// 步骤4: 等待验证码并填入
	return ac.waitForVerificationCode(statusCallback, codeContainer)
}

// waitForVerificationCode 等待验证码并填入
func (ac *AutomationClient) waitForVerificationCode(statusCallback func(string), codeContainer *string) error {
	timeout := time.After(time.Duration(MaxWaitSeconds) * time.Second)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待验证码超时")
		case <-ticker.C:
			if codeContainer != nil && *codeContainer != "" {
				code := *codeContainer
				statusCallback(fmt.Sprintf("收到验证码: %s，正在填入...", code))
				
				// 填入验证码
				err := chromedp.Run(ac.ctx,
					chromedp.SendKeys(`input[type="text"]`, code, chromedp.ByQuery),
					chromedp.Sleep(utils.GetRandomWaitTime("0.1-0.3")),
					chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),
				)
				if err != nil {
					return fmt.Errorf("填入验证码失败: %v", err)
				}
				
				statusCallback("验证码已提交，等待验证结果...")
				
				// 等待验证结果
				return ac.waitForLoginResult(statusCallback)
			}
		}
	}
}

// waitForLoginResult 等待登录结果
func (ac *AutomationClient) waitForLoginResult(statusCallback func(string)) error {
	// 等待页面跳转或错误消息
	err := chromedp.Run(ac.ctx,
		chromedp.Sleep(utils.GetRandomWaitTime("2-3")),
	)
	if err != nil {
		return err
	}
	
	// 检查是否有错误消息
	var errorText string
	err = chromedp.Run(ac.ctx,
		chromedp.Text(`.error, .alert-danger, [role="alert"]`, &errorText, chromedp.ByQuery),
	)
	
	if err == nil && errorText != "" {
		statusCallback(fmt.Sprintf("登录失败: %s", errorText))
		return fmt.Errorf("登录失败: %s", errorText)
	}
	
	// 检查是否成功跳转
	var currentURL string
	err = chromedp.Run(ac.ctx,
		chromedp.Location(&currentURL),
	)
	if err != nil {
		return err
	}
	
	if strings.Contains(currentURL, "dashboard") || strings.Contains(currentURL, "app") {
		statusCallback("登录成功！页面已跳转到仪表板。")
		return nil
	}
	
	statusCallback("登录状态未知，请手动检查页面。")
	return nil
}

// RunAutoLoginFlow 运行自动登录流程
func RunAutoLoginFlow(monitoringEmail, loginEmail, password string, statusCallback func(string)) error {
	if loginEmail == "" {
		statusCallback("错误: 登录邮箱为空")
		return fmt.Errorf("登录邮箱为空")
	}
	
	if password == "" {
		statusCallback("错误: 邮箱密码为空")
		return fmt.Errorf("邮箱密码为空")
	}
	
	statusCallback("正在获取当前Chrome浏览器URL...")
	
	// 获取当前Chrome URL
	chromeWindow, err := utils.GetCurrentChromeURL()
	if err != nil {
		statusCallback("错误: 获取URL失败，请确保Chrome在前台打开了一个页面。")
		return err
	}
	
	targetURL := chromeWindow.URL
	if targetURL == "" {
		targetURL = "https://cursor.sh" // 默认URL
	}
	
	statusCallback(fmt.Sprintf("成功获取URL: %s", targetURL))
	statusCallback(fmt.Sprintf("使用随机邮箱进行登录: %s", loginEmail))
	
	// 关闭原始Chrome窗口
	statusCallback("正在关闭原始Chrome窗口...")
	err = chromeWindow.Close()
	if err != nil {
		statusCallback(fmt.Sprintf("警告: 关闭原始窗口失败: %v", err))
	} else {
		statusCallback("原始窗口已关闭。")
	}
	
	// 创建自动化客户端
	client := NewAutomationClient()
	
	// 用于接收验证码的容器
	var codeContainer string
	
	// 启动邮箱监控（这里需要与邮箱模块集成）
	// TODO: 集成邮箱监控
	
	// 执行自动登录
	return client.AutomateLogin(targetURL, loginEmail, statusCallback, &codeContainer)
}
