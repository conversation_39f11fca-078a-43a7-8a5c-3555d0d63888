package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Email   EmailConfig   `mapstructure:"email"`
	Browser BrowserConfig `mapstructure:"browser"`
	Timing  TimingConfig  `mapstructure:"timing"`
	Utils   UtilsConfig   `mapstructure:"utils"`
	OAuth   OAuthConfig   `mapstructure:"oauth"`
	Token   TokenConfig   `mapstructure:"token"`
}

// EmailConfig 邮箱配置
type EmailConfig struct {
	Prefix   string `mapstructure:"prefix"`
	Password string `mapstructure:"password"`
}

// BrowserConfig 浏览器配置
type BrowserConfig struct {
	ChromePath string `mapstructure:"chrome_path"`
}

// TimingConfig 时间配置
type TimingConfig struct {
	MinRandomTime              float64 `mapstructure:"min_random_time"`
	MaxRandomTime              float64 `mapstructure:"max_random_time"`
	PageLoadWait               string  `mapstructure:"page_load_wait"`
	InputWait                  string  `mapstructure:"input_wait"`
	SubmitWait                 string  `mapstructure:"submit_wait"`
	VerificationCodeInput      string  `mapstructure:"verification_code_input"`
	VerificationSuccessWait    string  `mapstructure:"verification_success_wait"`
	VerificationRetryWait      string  `mapstructure:"verification_retry_wait"`
	EmailCheckInitialWait      string  `mapstructure:"email_check_initial_wait"`
	EmailRefreshWait           string  `mapstructure:"email_refresh_wait"`
	SettingsPageLoadWait       string  `mapstructure:"settings_page_load_wait"`
	FailedRetryTime            string  `mapstructure:"failed_retry_time"`
	RetryInterval              string  `mapstructure:"retry_interval"`
	MaxTimeout                 int     `mapstructure:"max_timeout"`
}

// UtilsConfig 工具配置
type UtilsConfig struct {
	EnabledUpdateCheck bool `mapstructure:"enabled_update_check"`
	EnabledForceUpdate bool `mapstructure:"enabled_force_update"`
	EnabledAccountInfo bool `mapstructure:"enabled_account_info"`
}

// OAuthConfig OAuth配置
type OAuthConfig struct {
	ShowSelectionAlert bool `mapstructure:"show_selection_alert"`
	Timeout            int  `mapstructure:"timeout"`
	MaxAttempts        int  `mapstructure:"max_attempts"`
}

// TokenConfig Token配置
type TokenConfig struct {
	RefreshServer string `mapstructure:"refresh_server"`
	EnableRefresh bool   `mapstructure:"enable_refresh"`
}

var globalConfig *Config

// GetUserDocumentsPath 获取用户文档路径
func GetUserDocumentsPath() string {
	if runtime.GOOS == "windows" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "."
		}
		return filepath.Join(homeDir, "Documents")
	}
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "."
	}
	return filepath.Join(homeDir, "Documents")
}

// GetDefaultBrowserPath 获取默认浏览器路径
func GetDefaultBrowserPath() string {
	if runtime.GOOS == "windows" {
		// 常见的Chrome安装路径
		paths := []string{
			`C:\Program Files\Google\Chrome\Application\chrome.exe`,
			`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
			`C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe`,
		}
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	}
	return ""
}

// InitConfig 初始化配置
func InitConfig() (*Config, error) {
	if globalConfig != nil {
		return globalConfig, nil
	}

	// 设置配置文件路径
	docsPath := GetUserDocumentsPath()
	configDir := filepath.Join(docsPath, "续杯工具")
	configFile := filepath.Join(configDir, "config.ini")

	// 确保配置目录存在
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 配置viper
	viper.SetConfigName("config")
	viper.SetConfigType("ini")
	viper.AddConfigPath(configDir)

	// 设置默认值
	setDefaults()

	// 尝试读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，创建默认配置
			if err := viper.WriteConfigAs(configFile); err != nil {
				return nil, fmt.Errorf("创建配置文件失败: %v", err)
			}
		} else {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}
	}

	// 解析配置到结构体
	config := &Config{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}

	globalConfig = config
	return config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// Email配置
	viper.SetDefault("email.prefix", "")
	viper.SetDefault("email.password", "")

	// Browser配置
	viper.SetDefault("browser.chrome_path", GetDefaultBrowserPath())

	// Timing配置
	viper.SetDefault("timing.min_random_time", 0.1)
	viper.SetDefault("timing.max_random_time", 0.8)
	viper.SetDefault("timing.page_load_wait", "0.1-0.8")
	viper.SetDefault("timing.input_wait", "0.3-0.8")
	viper.SetDefault("timing.submit_wait", "0.5-1.5")
	viper.SetDefault("timing.verification_code_input", "0.1-0.3")
	viper.SetDefault("timing.verification_success_wait", "2-3")
	viper.SetDefault("timing.verification_retry_wait", "2-3")
	viper.SetDefault("timing.email_check_initial_wait", "4-6")
	viper.SetDefault("timing.email_refresh_wait", "2-4")
	viper.SetDefault("timing.settings_page_load_wait", "1-2")
	viper.SetDefault("timing.failed_retry_time", "0.5-1")
	viper.SetDefault("timing.retry_interval", "8-12")
	viper.SetDefault("timing.max_timeout", 160)

	// Utils配置
	viper.SetDefault("utils.enabled_update_check", true)
	viper.SetDefault("utils.enabled_force_update", false)
	viper.SetDefault("utils.enabled_account_info", true)

	// OAuth配置
	viper.SetDefault("oauth.show_selection_alert", false)
	viper.SetDefault("oauth.timeout", 120)
	viper.SetDefault("oauth.max_attempts", 3)

	// Token配置
	viper.SetDefault("token.refresh_server", "https://token.cursorpro.com.cn")
	viper.SetDefault("token.enable_refresh", true)
}

// GetConfig 获取全局配置
func GetConfig() *Config {
	if globalConfig == nil {
		config, err := InitConfig()
		if err != nil {
			panic(fmt.Sprintf("初始化配置失败: %v", err))
		}
		return config
	}
	return globalConfig
}

// SaveConfig 保存配置
func SaveConfig() error {
	return viper.WriteConfig()
}

// UpdateEmailConfig 更新邮箱配置
func UpdateEmailConfig(prefix, password string) error {
	viper.Set("email.prefix", prefix)
	viper.Set("email.password", password)
	
	// 更新全局配置
	if globalConfig != nil {
		globalConfig.Email.Prefix = prefix
		globalConfig.Email.Password = password
	}
	
	return SaveConfig()
}

// UpdateBrowserConfig 更新浏览器配置
func UpdateBrowserConfig(chromePath string) error {
	viper.Set("browser.chrome_path", chromePath)
	
	// 更新全局配置
	if globalConfig != nil {
		globalConfig.Browser.ChromePath = chromePath
	}
	
	return SaveConfig()
}
