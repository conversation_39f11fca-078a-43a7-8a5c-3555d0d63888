package email

import (
	"bufio"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

const (
	POP3Server      = "pop.2925.com"
	POP3Port        = 110
	RefreshInterval = 1 * time.Second
)

// Client POP3客户端
type Client struct {
	conn     net.Conn
	reader   *bufio.Reader
	username string
	password string
}

// Message 邮件消息
type Message struct {
	ID      int
	UID     string
	Subject string
	Body    string
	From    string
	Date    time.Time
}

// NewClient 创建新的POP3客户端
func NewClient(username, password string) *Client {
	return &Client{
		username: username,
		password: password,
	}
}

// Connect 连接到POP3服务器
func (c *Client) Connect() error {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", POP3Server, POP3Port), 20*time.Second)
	if err != nil {
		return fmt.Errorf("连接服务器失败: %v", err)
	}

	c.conn = conn
	c.reader = bufio.NewReader(conn)

	// 读取欢迎消息
	_, err = c.readResponse()
	if err != nil {
		return fmt.Errorf("读取欢迎消息失败: %v", err)
	}

	return nil
}

// Login 登录
func (c *Client) Login() error {
	// 发送USER命令
	err := c.sendCommand(fmt.Sprintf("USER %s", c.username))
	if err != nil {
		return fmt.Errorf("发送USER命令失败: %v", err)
	}

	_, err = c.readResponse()
	if err != nil {
		return fmt.Errorf("USER命令响应错误: %v", err)
	}

	// 发送PASS命令
	err = c.sendCommand(fmt.Sprintf("PASS %s", c.password))
	if err != nil {
		return fmt.Errorf("发送PASS命令失败: %v", err)
	}

	response, err := c.readResponse()
	if err != nil {
		return fmt.Errorf("PASS命令响应错误: %v", err)
	}

	if !strings.HasPrefix(response, "+OK") {
		return fmt.Errorf("登录失败: %s", response)
	}

	return nil
}

// GetMessageCount 获取邮件数量
func (c *Client) GetMessageCount() (int, error) {
	err := c.sendCommand("STAT")
	if err != nil {
		return 0, err
	}

	response, err := c.readResponse()
	if err != nil {
		return 0, err
	}

	if !strings.HasPrefix(response, "+OK") {
		return 0, fmt.Errorf("STAT命令失败: %s", response)
	}

	parts := strings.Fields(response)
	if len(parts) < 2 {
		return 0, fmt.Errorf("STAT响应格式错误: %s", response)
	}

	count, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, fmt.Errorf("解析邮件数量失败: %v", err)
	}

	return count, nil
}

// GetUIDList 获取UID列表
func (c *Client) GetUIDList() (map[string]int, error) {
	err := c.sendCommand("UIDL")
	if err != nil {
		return nil, err
	}

	response, err := c.readResponse()
	if err != nil {
		return nil, err
	}

	if !strings.HasPrefix(response, "+OK") {
		return nil, fmt.Errorf("UIDL命令失败: %s", response)
	}

	uidMap := make(map[string]int)

	// 读取多行响应
	for {
		line, err := c.reader.ReadString('\n')
		if err != nil {
			return nil, err
		}

		line = strings.TrimSpace(line)
		if line == "." {
			break
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			msgNum, err := strconv.Atoi(parts[0])
			if err == nil {
				uidMap[parts[1]] = msgNum
			}
		}
	}

	return uidMap, nil
}

// RetrieveMessage 获取邮件内容
func (c *Client) RetrieveMessage(msgNum int) (*Message, error) {
	err := c.sendCommand(fmt.Sprintf("RETR %d", msgNum))
	if err != nil {
		return nil, err
	}

	response, err := c.readResponse()
	if err != nil {
		return nil, err
	}

	if !strings.HasPrefix(response, "+OK") {
		return nil, fmt.Errorf("RETR命令失败: %s", response)
	}

	// 读取邮件内容
	var content strings.Builder
	for {
		line, err := c.reader.ReadString('\n')
		if err != nil {
			return nil, err
		}

		if strings.TrimSpace(line) == "." {
			break
		}

		content.WriteString(line)
	}

	// 简化邮件解析，直接返回内容
	return &Message{
		ID:      msgNum,
		Subject: "Email",
		Body:    content.String(),
		From:    "unknown",
		Date:    time.Now(),
	}, nil
}

// Quit 退出连接
func (c *Client) Quit() error {
	if c.conn == nil {
		return nil
	}

	err := c.sendCommand("QUIT")
	if err != nil {
		c.conn.Close()
		return err
	}

	_, err = c.readResponse()
	c.conn.Close()
	return err
}

// sendCommand 发送命令
func (c *Client) sendCommand(command string) error {
	_, err := c.conn.Write([]byte(command + "\r\n"))
	return err
}

// readResponse 读取响应
func (c *Client) readResponse() (string, error) {
	response, err := c.reader.ReadString('\n')
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(response), nil
}

// ExtractVerificationCode 从邮件内容中提取验证码
func ExtractVerificationCode(content string) string {
	// 常见的验证码模式
	patterns := []string{
		`(?i)verification\s*code[:\s]*([A-Z0-9]{6})`,
		`(?i)verify\s*code[:\s]*([A-Z0-9]{6})`,
		`(?i)code[:\s]*([A-Z0-9]{6})`,
		`([A-Z0-9]{6})`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			code := strings.TrimSpace(matches[1])
			if len(code) == 6 {
				return code
			}
		}
	}

	return ""
}

// CleanEmailBody 清理邮件内容
func CleanEmailBody(body string) string {
	// 移除HTML标签
	re := regexp.MustCompile(`<[^>]*>`)
	cleaned := re.ReplaceAllString(body, "")

	// 移除多余的空白字符
	re = regexp.MustCompile(`\s+`)
	cleaned = re.ReplaceAllString(cleaned, " ")

	return strings.TrimSpace(cleaned)
}

// Monitor 邮箱监控器
type Monitor struct {
	client     *Client
	seenUIDs   map[string]bool
	outputChan chan string
	stopChan   chan bool
	isRunning  bool
}

// NewMonitor 创建新的邮箱监控器
func NewMonitor(username, password string) *Monitor {
	return &Monitor{
		client:     NewClient(username, password),
		seenUIDs:   make(map[string]bool),
		outputChan: make(chan string, 100),
		stopChan:   make(chan bool, 1),
		isRunning:  false,
	}
}

// Start 开始监控
func (m *Monitor) Start() error {
	if m.isRunning {
		return fmt.Errorf("监控器已在运行")
	}

	// 连接并登录
	err := m.client.Connect()
	if err != nil {
		return fmt.Errorf("连接失败: %v", err)
	}

	err = m.client.Login()
	if err != nil {
		m.client.Quit()
		return fmt.Errorf("登录失败: %v", err)
	}

	m.outputChan <- fmt.Sprintf("正在监控邮箱: %s", m.client.username)
	m.outputChan <- "验证成功。"

	// 建立基线
	err = m.establishBaseline()
	if err != nil {
		m.client.Quit()
		return fmt.Errorf("建立基线失败: %v", err)
	}

	m.client.Quit()
	m.isRunning = true

	// 开始监控循环
	go m.monitorLoop()

	return nil
}

// Stop 停止监控
func (m *Monitor) Stop() {
	if m.isRunning {
		m.stopChan <- true
		m.isRunning = false
	}
}

// GetOutputChan 获取输出通道
func (m *Monitor) GetOutputChan() <-chan string {
	return m.outputChan
}

// establishBaseline 建立基线
func (m *Monitor) establishBaseline() error {
	uidMap, err := m.client.GetUIDList()
	if err != nil {
		return err
	}

	for uid := range uidMap {
		m.seenUIDs[uid] = true
	}

	m.outputChan <- fmt.Sprintf("基线已建立，当前有 %d 封邮件。", len(uidMap))
	return nil
}

// monitorLoop 监控循环
func (m *Monitor) monitorLoop() {
	defer func() {
		m.isRunning = false
		close(m.outputChan)
	}()

	loopCounter := 0

	for {
		select {
		case <-m.stopChan:
			return
		default:
			// 连接并检查新邮件
			err := m.checkNewEmails()
			if err != nil {
				m.outputChan <- fmt.Sprintf("检查邮件时出错: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}

			loopCounter++
			if loopCounter%30 == 0 { // 每30次循环显示一次状态
				m.outputChan <- "正在监控中..."
			}

			time.Sleep(RefreshInterval)
		}
	}
}

// checkNewEmails 检查新邮件
func (m *Monitor) checkNewEmails() error {
	// 重新连接
	err := m.client.Connect()
	if err != nil {
		return err
	}
	defer m.client.Quit()

	err = m.client.Login()
	if err != nil {
		return err
	}

	// 获取当前UID列表
	currentUIDs, err := m.client.GetUIDList()
	if err != nil {
		return err
	}

	// 找出新的UID
	var newUIDs []string
	var newMsgNums []int

	for uid, msgNum := range currentUIDs {
		if !m.seenUIDs[uid] {
			newUIDs = append(newUIDs, uid)
			newMsgNums = append(newMsgNums, msgNum)
			m.seenUIDs[uid] = true
		}
	}

	if len(newUIDs) > 0 {
		m.outputChan <- fmt.Sprintf("\n发现 %d 封新邮件，正在检查...", len(newUIDs))

		// 检查新邮件中的验证码
		for _, msgNum := range newMsgNums {
			msg, err := m.client.RetrieveMessage(msgNum)
			if err != nil {
				m.outputChan <- fmt.Sprintf("获取邮件失败: %v", err)
				continue
			}

			cleanBody := CleanEmailBody(msg.Body)
			code := ExtractVerificationCode(cleanBody)

			if code != "" {
				m.outputChan <- fmt.Sprintf("成功提取到新邮件中的验证码: %s", code)
				m.outputChan <- fmt.Sprintf("VERIFICATION_CODE:%s", code)
				return nil // 找到验证码后退出
			}
		}
	}

	return nil
}
