package gui

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	appmanager "cursor-manager-go/internal/app"
	"cursor-manager-go/internal/browser"
	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/email"
	"cursor-manager-go/internal/utils"
)

// App GUI应用程序
type App struct {
	fyneApp fyne.App
	window  fyne.Window
	config  *config.Config

	// UI组件
	statusText    *widget.Entry
	emailEntry    *widget.Entry
	prefixEntry   *widget.Entry
	passwordEntry *widget.Entry
	browserEntry  *widget.Entry

	// 按钮
	autoLoginBtn     *widget.Button
	resetEnvBtn      *widget.Button
	emailClientBtn   *widget.Button
	saveCredBtn      *widget.Button
	browseBrowserBtn *widget.Button
	saveBrowserBtn   *widget.Button
	copyEmailBtn     *widget.Button

	// 状态
	isRunning    bool
	emailMonitor *email.Monitor
}

// NewApp 创建新的GUI应用
func NewApp() *App {
	fyneApp := app.NewWithID("cursor-manager-go")
	fyneApp.SetIcon(fyne.NewStaticResource("icon", []byte{})) // 可以添加图标

	// 初始化配置
	cfg, err := config.InitConfig()
	if err != nil {
		dialog.ShowError(fmt.Errorf("初始化配置失败: %v", err), nil)
		return nil
	}

	return &App{
		fyneApp: fyneApp,
		config:  cfg,
	}
}

// Run 运行应用程序
func (a *App) Run() {
	a.window = a.fyneApp.NewWindow("续杯工具")
	a.window.Resize(fyne.NewSize(850, 600))
	a.window.SetFixedSize(false)

	// 创建UI
	a.createUI()

	// 设置窗口关闭事件
	a.window.SetCloseIntercept(func() {
		if a.emailMonitor != nil {
			a.emailMonitor.Stop()
		}
		a.fyneApp.Quit()
	})

	// 启动随机邮箱更新
	go a.updateRandomEmail()

	a.window.ShowAndRun()
}

// createUI 创建用户界面
func (a *App) createUI() {
	// 左侧控制面板
	leftPanel := a.createLeftPanel()

	// 右侧状态显示
	rightPanel := a.createRightPanel()

	// 主容器
	content := container.NewHSplit(leftPanel, rightPanel)
	content.SetOffset(0.4) // 设置分割比例

	a.window.SetContent(content)

	// 初始状态
	a.updateStatus("准备就绪。请点击按钮开始。")
}

// createLeftPanel 创建左侧控制面板
func (a *App) createLeftPanel() *fyne.Container {
	// 信息标签
	infoLabel := widget.NewLabel("请选择一个操作：")

	// 一键登录区域
	autoLoginFrame := a.createAutoLoginFrame()

	// 邮箱凭据区域
	credentialsFrame := a.createCredentialsFrame()

	// 浏览器设置区域
	browserFrame := a.createBrowserFrame()

	// 账号信息区域
	accountFrame := a.createAccountFrame()

	// 系统工具区域
	systemFrame := a.createSystemFrame()

	return container.NewVBox(
		infoLabel,
		autoLoginFrame,
		credentialsFrame,
		browserFrame,
		accountFrame,
		systemFrame,
	)
}

// createRightPanel 创建右侧状态面板
func (a *App) createRightPanel() *fyne.Container {
	a.statusText = widget.NewMultiLineEntry()
	a.statusText.SetText("")
	a.statusText.Wrapping = fyne.TextWrapWord

	scroll := container.NewScroll(a.statusText)
	scroll.SetMinSize(fyne.NewSize(400, 500))

	return container.NewBorder(
		widget.NewLabel("状态信息"),
		nil, nil, nil,
		scroll,
	)
}

// createAutoLoginFrame 创建一键登录框架
func (a *App) createAutoLoginFrame() *widget.Card {
	a.autoLoginBtn = widget.NewButton("🔑 一键登录 (当前页面)", a.handleAutoLogin)

	return widget.NewCard("一键登录工具", "", container.NewVBox(a.autoLoginBtn))
}

// createCredentialsFrame 创建凭据设置框架
func (a *App) createCredentialsFrame() *widget.Card {
	a.prefixEntry = widget.NewEntry()
	a.prefixEntry.SetText(a.config.Email.Prefix)
	a.prefixEntry.SetPlaceHolder("邮箱前缀")

	a.passwordEntry = widget.NewPasswordEntry()
	a.passwordEntry.SetText(a.config.Email.Password)
	a.passwordEntry.SetPlaceHolder("邮箱密码")

	a.saveCredBtn = widget.NewButton("保存凭据", a.handleSaveCredentials)

	form := container.NewVBox(
		widget.NewFormItem("邮箱前缀:", a.prefixEntry).Widget,
		widget.NewFormItem("邮箱密码:", a.passwordEntry).Widget,
		a.saveCredBtn,
	)

	return widget.NewCard("邮箱凭据设置", "", form)
}

// createBrowserFrame 创建浏览器设置框架
func (a *App) createBrowserFrame() *widget.Card {
	a.browserEntry = widget.NewEntry()
	a.browserEntry.SetText(a.config.Browser.ChromePath)
	a.browserEntry.SetPlaceHolder("Chrome浏览器路径")

	a.browseBrowserBtn = widget.NewButton("浏览...", a.handleBrowseBrowser)
	a.saveBrowserBtn = widget.NewButton("保存路径", a.handleSaveBrowser)

	pathContainer := container.NewBorder(nil, nil, nil, a.browseBrowserBtn, a.browserEntry)

	form := container.NewVBox(
		widget.NewFormItem("Chrome路径:", pathContainer).Widget,
		a.saveBrowserBtn,
	)

	return widget.NewCard("浏览器设置", "", form)
}

// createAccountFrame 创建账号信息框架
func (a *App) createAccountFrame() *widget.Card {
	a.emailEntry = widget.NewEntry()
	a.emailEntry.SetText("")

	a.copyEmailBtn = widget.NewButton("复制", a.handleCopyEmail)

	emailContainer := container.NewBorder(nil, nil, nil, a.copyEmailBtn, a.emailEntry)

	form := container.NewVBox(
		widget.NewFormItem("邮箱:", emailContainer).Widget,
	)

	return widget.NewCard("账号信息 (方便复制)", "", form)
}

// createSystemFrame 创建系统工具框架
func (a *App) createSystemFrame() *widget.Card {
	a.resetEnvBtn = widget.NewButton("一键重置环境", a.handleResetEnvironment)
	a.emailClientBtn = widget.NewButton("打开邮箱客户端", a.handleOpenEmailClient)

	return widget.NewCard("系统工具", "", container.NewVBox(
		a.resetEnvBtn,
		a.emailClientBtn,
	))
}

// updateStatus 更新状态信息
func (a *App) updateStatus(message string) {
	// 处理特殊的验证码消息
	if strings.HasPrefix(message, "COPY_AND_SHOW:") {
		code := strings.TrimPrefix(message, "COPY_AND_SHOW:")
		a.copyToClipboard(code, "验证码")
		message = fmt.Sprintf("成功获取到验证码: %s (已复制)", code)
	}

	// 添加时间戳
	timestamp := time.Now().Format("15:04:05")
	fullMessage := fmt.Sprintf("[%s] %s\n", timestamp, message)

	// 更新状态文本
	currentText := a.statusText.Text
	a.statusText.SetText(currentText + fullMessage)

	// 滚动到底部
	a.statusText.CursorRow = len(strings.Split(a.statusText.Text, "\n"))
}

// copyToClipboard 复制到剪贴板
func (a *App) copyToClipboard(text, itemName string) {
	clipboard := a.window.Clipboard()
	clipboard.SetContent(text)
	a.updateStatus(fmt.Sprintf("'%s' 已复制到剪贴板。", itemName))
}

// updateRandomEmail 更新随机邮箱
func (a *App) updateRandomEmail() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if a.config.Email.Prefix != "" {
				client := browser.NewAutomationClient()
				randomEmail := client.GenerateRandomEmail()
				a.emailEntry.SetText(randomEmail)
			}
		}
	}
}

// clearStatusAndDisableButtons 清空状态并禁用按钮
func (a *App) clearStatusAndDisableButtons() {
	a.statusText.SetText("")
	a.autoLoginBtn.Disable()
	a.resetEnvBtn.Disable()
	a.emailClientBtn.Disable()
}

// enableButtons 启用按钮
func (a *App) enableButtons() {
	a.autoLoginBtn.Enable()
	a.resetEnvBtn.Enable()
	a.emailClientBtn.Enable()
}

// handleAutoLogin 处理一键登录
func (a *App) handleAutoLogin() {
	go func() {
		a.clearStatusAndDisableButtons()
		defer a.enableButtons()

		password := a.passwordEntry.Text
		monitoringEmail := fmt.Sprintf("%<EMAIL>", a.config.Email.Prefix)
		loginEmail := a.emailEntry.Text

		if password == "" {
			a.updateStatus("错误: 请先设置邮箱密码")
			return
		}

		if loginEmail == "" {
			a.updateStatus("错误: 登录邮箱为空")
			return
		}

		err := browser.RunAutoLoginFlow(monitoringEmail, loginEmail, password, a.updateStatus)
		if err != nil {
			a.updateStatus(fmt.Sprintf("错误: 一键登录失败: %v", err))
		}
	}()
}

// handleResetEnvironment 处理环境重置
func (a *App) handleResetEnvironment() {
	go func() {
		a.clearStatusAndDisableButtons()
		defer a.enableButtons()

		manager := appmanager.NewManager(a.updateStatus)
		err := manager.RunFullResetFlow()
		if err != nil {
			a.updateStatus(fmt.Sprintf("%s 环境重置失败: %v", utils.Emoji["ERROR"], err))
		}
	}()
}

// handleOpenEmailClient 处理打开邮箱客户端
func (a *App) handleOpenEmailClient() {
	go func() {
		emailToWatch := fmt.Sprintf("%<EMAIL>", a.config.Email.Prefix)
		password := a.passwordEntry.Text

		if a.config.Email.Prefix == "" || password == "" {
			a.updateStatus("错误: 请先设置凭据。")
			return
		}

		// 停止之前的监控
		if a.emailMonitor != nil {
			a.emailMonitor.Stop()
		}

		// 创建新的邮箱监控
		a.emailMonitor = email.NewMonitor(emailToWatch, password)

		// 启动监控
		err := a.emailMonitor.Start()
		if err != nil {
			a.updateStatus(fmt.Sprintf("启动邮箱监控失败: %v", err))
			return
		}

		// 监听输出
		go func() {
			for message := range a.emailMonitor.GetOutputChan() {
				if strings.Contains(message, "VERIFICATION_CODE:") {
					code := strings.TrimPrefix(message, "VERIFICATION_CODE:")
					a.updateStatus(fmt.Sprintf("COPY_AND_SHOW:%s", code))
				} else {
					a.updateStatus(message)
				}
			}
		}()
	}()
}

// handleSaveCredentials 处理保存凭据
func (a *App) handleSaveCredentials() {
	prefix := strings.TrimSpace(a.prefixEntry.Text)
	password := a.passwordEntry.Text

	err := config.UpdateEmailConfig(prefix, password)
	if err != nil {
		a.updateStatus("错误: 保存凭据失败。")
		dialog.ShowError(fmt.Errorf("保存凭据失败: %v", err), a.window)
		return
	}

	a.config.Email.Prefix = prefix
	a.config.Email.Password = password
	a.updateStatus(fmt.Sprintf("%s 凭据已保存。", utils.Emoji["SUCCESS"]))

	// 刷新随机邮箱显示
	if prefix != "" {
		client := browser.NewAutomationClient()
		randomEmail := client.GenerateRandomEmail()
		a.emailEntry.SetText(randomEmail)
	}
}

// handleBrowseBrowser 处理浏览器路径浏览
func (a *App) handleBrowseBrowser() {
	dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil || reader == nil {
			return
		}
		defer reader.Close()

		path := reader.URI().Path()
		a.browserEntry.SetText(path)
		a.updateStatus(fmt.Sprintf("浏览器路径已更新为: %s", path))
	}, a.window)
}

// handleSaveBrowser 处理保存浏览器路径
func (a *App) handleSaveBrowser() {
	path := strings.TrimSpace(a.browserEntry.Text)

	if path == "" {
		a.updateStatus("错误: 路径不能为空")
		return
	}

	err := config.UpdateBrowserConfig(path)
	if err != nil {
		a.updateStatus("错误: 保存浏览器路径失败。")
		dialog.ShowError(fmt.Errorf("保存浏览器路径失败: %v", err), a.window)
		return
	}

	a.config.Browser.ChromePath = path
	a.updateStatus(fmt.Sprintf("%s 浏览器路径已保存。", utils.Emoji["SUCCESS"]))
}

// handleCopyEmail 处理复制邮箱
func (a *App) handleCopyEmail() {
	email := a.emailEntry.Text
	if email == "" {
		a.updateStatus("错误: 邮箱为空")
		return
	}

	a.copyToClipboard(email, "邮箱")
}
