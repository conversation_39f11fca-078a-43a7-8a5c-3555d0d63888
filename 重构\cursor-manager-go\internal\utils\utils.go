package utils

import (
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

// Emoji 常量定义
var Emoji = map[string]string{
	"INFO":    "信息: ",
	"ERROR":   "错误: ",
	"SUCCESS": "成功: ",
	"WARNING": "警告: ",
	"LOGIN":   "",
}

// GetUserDocumentsPath 获取用户文档路径
func GetUserDocumentsPath() string {
	if runtime.GOOS == "windows" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "."
		}
		return filepath.Join(homeDir, "Documents")
	}
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "."
	}
	return filepath.Join(homeDir, "Documents")
}

// GetLinuxCursorPath 获取Linux下Cursor路径
func GetLinuxCursorPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return ""
	}
	return filepath.Join(homeDir, ".cursor")
}

// GetDefaultDriverPath 获取默认驱动路径
func GetDefaultDriverPath() string {
	if runtime.GOOS == "windows" {
		return filepath.Join(GetUserDocumentsPath(), "续杯工具", "drivers")
	}
	return ""
}

// GetDefaultBrowserPath 获取默认浏览器路径
func GetDefaultBrowserPath() string {
	if runtime.GOOS == "windows" {
		paths := []string{
			`C:\Program Files\Google\Chrome\Application\chrome.exe`,
			`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
		}
		
		// 添加用户特定路径
		if username := os.Getenv("USERNAME"); username != "" {
			userPath := fmt.Sprintf(`C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe`, username)
			paths = append(paths, userPath)
		}
		
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	}
	return ""
}

// ChromeWindow Windows API结构体和函数
type ChromeWindow struct {
	Handle syscall.Handle
	URL    string
}

// GetCurrentChromeURL 获取当前Chrome浏览器URL
func GetCurrentChromeURL() (*ChromeWindow, error) {
	if runtime.GOOS != "windows" {
		return nil, fmt.Errorf("此功能仅支持Windows系统")
	}

	// 查找Chrome窗口
	var chromeWindow *ChromeWindow
	
	// 枚举所有窗口
	enumProc := syscall.NewCallback(func(hwnd syscall.Handle, lParam uintptr) uintptr {
		// 获取窗口类名
		className := make([]uint16, 256)
		ret, _, _ := syscall.Syscall(getClassName, 3, uintptr(hwnd), uintptr(unsafe.Pointer(&className[0])), 256)
		if ret == 0 {
			return 1 // 继续枚举
		}
		
		classNameStr := syscall.UTF16ToString(className)
		if classNameStr != "Chrome_WidgetWin_1" {
			return 1 // 继续枚举
		}
		
		// 获取窗口标题
		titleBuf := make([]uint16, 512)
		ret, _, _ = syscall.Syscall(getWindowText, 3, uintptr(hwnd), uintptr(unsafe.Pointer(&titleBuf[0])), 512)
		if ret == 0 {
			return 1 // 继续枚举
		}
		
		title := syscall.UTF16ToString(titleBuf)
		if !strings.Contains(title, "Google Chrome") {
			return 1 // 继续枚举
		}
		
		// 尝试获取URL（这里简化处理，实际可能需要更复杂的方法）
		url := extractURLFromTitle(title)
		
		chromeWindow = &ChromeWindow{
			Handle: hwnd,
			URL:    url,
		}
		
		return 0 // 停止枚举
	})
	
	ret, _, _ := syscall.Syscall(enumWindows, 2, enumProc, 0, 0)
	if ret == 0 {
		return nil, fmt.Errorf("枚举窗口失败")
	}
	
	if chromeWindow == nil {
		return nil, fmt.Errorf("未找到Chrome窗口")
	}
	
	return chromeWindow, nil
}

// extractURLFromTitle 从窗口标题提取URL
func extractURLFromTitle(title string) string {
	// Chrome窗口标题通常格式为 "页面标题 - Google Chrome"
	// 这里简化处理，实际应用中可能需要更复杂的URL提取逻辑
	if strings.Contains(title, " - Google Chrome") {
		// 这里可以添加更复杂的URL提取逻辑
		// 暂时返回一个占位符
		return "https://cursor.sh" // 默认URL
	}
	return ""
}

// CloseWindow 关闭窗口
func (cw *ChromeWindow) Close() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("此功能仅支持Windows系统")
	}
	
	ret, _, _ := syscall.Syscall(closeWindow, 1, uintptr(cw.Handle), 0, 0)
	if ret == 0 {
		return fmt.Errorf("关闭窗口失败")
	}
	
	return nil
}

// Windows API函数声明
var (
	user32           = windows.NewLazySystemDLL("user32.dll")
	enumWindows      = user32.NewProc("EnumWindows").Addr()
	getClassName     = user32.NewProc("GetClassNameW").Addr()
	getWindowText    = user32.NewProc("GetWindowTextW").Addr()
	closeWindow      = user32.NewProc("CloseWindow").Addr()
)

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	rand.Seed(time.Now().UnixNano())
	
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// ParseTimeRange 解析时间范围字符串 (如 "0.1-0.8")
func ParseTimeRange(timeRange string) (float64, float64, error) {
	parts := strings.Split(timeRange, "-")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("无效的时间范围格式: %s", timeRange)
	}
	
	min, err := strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
	if err != nil {
		return 0, 0, fmt.Errorf("解析最小值失败: %v", err)
	}
	
	max, err := strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
	if err != nil {
		return 0, 0, fmt.Errorf("解析最大值失败: %v", err)
	}
	
	return min, max, nil
}

// GetRandomWaitTime 获取随机等待时间
func GetRandomWaitTime(timeRange string) time.Duration {
	min, max, err := ParseTimeRange(timeRange)
	if err != nil {
		// 如果解析失败，返回默认值
		return time.Duration(rand.Float64()*1000) * time.Millisecond
	}
	
	randomTime := min + rand.Float64()*(max-min)
	return time.Duration(randomTime*1000) * time.Millisecond
}

// IsProcessRunning 检查进程是否运行
func IsProcessRunning(processName string) bool {
	if runtime.GOOS == "windows" {
		cmd := exec.Command("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName))
		output, err := cmd.Output()
		if err != nil {
			return false
		}
		return strings.Contains(string(output), processName)
	}
	return false
}

// KillProcess 终止进程
func KillProcess(processName string) error {
	if runtime.GOOS == "windows" {
		cmd := exec.Command("taskkill", "/F", "/IM", processName)
		return cmd.Run()
	}
	return fmt.Errorf("不支持的操作系统")
}

// OpenURL 打开URL
func OpenURL(url string) error {
	var cmd *exec.Cmd
	
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("rundll32", "url.dll,FileProtocolHandler", url)
	case "darwin":
		cmd = exec.Command("open", url)
	case "linux":
		cmd = exec.Command("xdg-open", url)
	default:
		return fmt.Errorf("不支持的操作系统")
	}
	
	return cmd.Start()
}

// CopyToClipboard 复制到剪贴板
func CopyToClipboard(text string) error {
	if runtime.GOOS == "windows" {
		cmd := exec.Command("clip")
		cmd.Stdin = strings.NewReader(text)
		return cmd.Run()
	}
	return fmt.Errorf("不支持的操作系统")
}
