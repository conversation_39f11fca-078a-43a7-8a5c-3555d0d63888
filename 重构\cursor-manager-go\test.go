package main

import (
	"fmt"
	"log"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
)

func main() {
	fmt.Println("测试Cursor管理工具Go版本...")
	
	// 测试配置模块
	fmt.Println("\n=== 测试配置模块 ===")
	cfg, err := config.InitConfig()
	if err != nil {
		log.Printf("配置初始化失败: %v", err)
	} else {
		fmt.Printf("配置初始化成功\n")
		fmt.Printf("邮箱前缀: %s\n", cfg.Email.Prefix)
		fmt.Printf("浏览器路径: %s\n", cfg.Browser.ChromePath)
	}
	
	// 测试工具模块
	fmt.Println("\n=== 测试工具模块 ===")
	fmt.Printf("用户文档路径: %s\n", utils.GetUserDocumentsPath())
	fmt.Printf("默认浏览器路径: %s\n", utils.GetDefaultBrowserPath())
	
	// 测试随机字符串生成
	randomStr := utils.GenerateRandomString(8)
	fmt.Printf("随机字符串: %s\n", randomStr)
	
	// 测试Emoji
	fmt.Printf("成功图标: %s\n", utils.Emoji["SUCCESS"])
	fmt.Printf("错误图标: %s\n", utils.Emoji["ERROR"])
	
	fmt.Println("\n测试完成！")
}
